/* Estilos adicionales para el portafolio de <PERSON> */

/* Optimización para scroll suave sin vibraciones */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Prevenir vibraciones en elementos animados */
.floating,
.trading-card,
.btn-primary,
.btn-gold {
    backface-visibility: hidden;
    perspective: 1000px;
    transform: translate3d(0, 0, 0);
}

/* Animaciones personalizadas */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-30px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(30px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Clases de utilidad para animaciones */
.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-slide-up {
    animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-right {
    animation: slideInRight 0.6s ease-out;
}

/* Efectos de hover mejorados */
.hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
    transition: box-shadow 0.3s ease;
}

/* Gradientes personalizados */
.gradient-text {
    background: linear-gradient(135deg, #ffd700 0%, #ff8c00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gradient-border {
    border: 2px solid transparent;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
}

/* Efectos de cristal (glassmorphism) */
.glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Estilos para el loader */
.loader {
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-left: 4px solid #ffd700;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Estilos para notificaciones */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    padding: 16px 24px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 400px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.notification.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.notification.info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* Estilos para el scroll personalizado */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1f2937;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #ffd700 0%, #ff8c00 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #ff8c00 0%, #ffd700 100%);
}

/* Efectos de partículas de fondo */
.particles-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

.particle {
    position: absolute;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.3;
    }
}

/* Estilos responsivos mejorados */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem !important;
    }
    
    .hero-subtitle {
        font-size: 1.25rem !important;
    }
    
    .trading-card {
        margin: 0 10px;
    }
    
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Estilos para el menú móvil */
.mobile-menu {
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.mobile-menu.open {
    transform: translateX(0);
}

/* Efectos de typing para texto */
.typing-effect {
    overflow: hidden;
    border-right: 2px solid #ffd700;
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: #ffd700; }
}

/* Estilos para tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #1f2937;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 14px;
    border: 1px solid #374151;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Estilos para el modo oscuro mejorado */
.dark-mode {
    background: #0f172a;
    color: #f1f5f9;
}

.dark-mode .trading-card {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.1);
}

/* Efectos de iluminación */
.glow-effect {
    position: relative;
}

.glow-effect::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ffd700, #ff8c00, #ffd700);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.glow-effect:hover::before {
    opacity: 1;
}

/* Estilos para contadores animados */
.counter-container {
    position: relative;
    overflow: hidden;
}

.counter-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 140, 0, 0.1) 100%);
    transform: translateX(-100%);
    animation: fillProgress 2s ease-out forwards;
}

@keyframes fillProgress {
    to {
        transform: translateX(0);
    }
}
