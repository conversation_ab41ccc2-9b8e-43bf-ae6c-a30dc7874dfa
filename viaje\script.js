// Animación de contadores
function animateCounters() {
    const counters = document.querySelectorAll('.counter');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 100;
        let current = 0;
        
        const updateCounter = () => {
            if (current < target) {
                current += increment;
                counter.textContent = Math.floor(current);
                setTimeout(updateCounter, 20);
            } else {
                counter.textContent = target;
            }
        };
        
        updateCounter();
    });
}

// Gráfico de Performance
function createPerformanceChart() {
    const ctx = document.getElementById('performanceChart').getContext('2d');

    // Datos simulados de performance (en un caso real, estos vendrían de tu API)
    const performanceData = {
        labels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
        datasets: [{
            label: 'ROI (%)',
            data: [8, 15, 22, 18, 35, 42, 58, 65, 78, 89, 105, 127],
            borderColor: '#ffd700',
            backgroundColor: 'rgba(255, 215, 0, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    };

    new Chart(ctx, {
        type: 'line',
        data: performanceData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9CA3AF'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9CA3AF'
                    }
                }
            }
        }
    });
}

// Gráfico de Distribución de Portafolio
function createPortfolioChart() {
    const ctx = document.getElementById('portfolioChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Forex', 'Crypto', 'Acciones'],
            datasets: [{
                data: [40, 35, 25],
                backgroundColor: ['#3B82F6', '#EAB308', '#10B981'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// Gráfico de Performance Detallado
function createDetailedPerformanceChart() {
    const ctx = document.getElementById('detailedPerformanceChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
            datasets: [
                {
                    label: 'Forex',
                    data: [5, 12, 18, 15, 28, 35, 45, 52, 62, 71, 85, 89],
                    borderColor: '#3B82F6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'Crypto',
                    data: [12, 25, 35, 28, 55, 68, 85, 95, 115, 135, 145, 156],
                    borderColor: '#EAB308',
                    backgroundColor: 'rgba(234, 179, 8, 0.1)',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'Acciones',
                    data: [8, 18, 25, 22, 38, 48, 65, 75, 88, 105, 120, 134],
                    borderColor: '#10B981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 2,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    labels: {
                        color: '#9CA3AF'
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9CA3AF'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9CA3AF'
                    }
                }
            }
        }
    });
}

// Smooth scrolling optimizado para navegación
function setupSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                // Usar requestAnimationFrame para scroll más suave
                const targetPosition = target.offsetTop - 80;
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Efectos de scroll optimizados
function setupScrollEffects() {
    // Reducir la frecuencia de observación para mejor performance
    const observerOptions = {
        threshold: 0.2,
        rootMargin: '0px 0px -30px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
                // Dejar de observar una vez animado
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observar elementos que queremos animar
    document.querySelectorAll('.trading-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Simulador de precios en tiempo real
function startPriceSimulator() {
    const priceElements = [
        { element: document.querySelector('.forex-price'), base: 1.0850, symbol: 'EUR/USD' },
        { element: document.querySelector('.crypto-price'), base: 45000, symbol: 'BTC/USD' },
        { element: document.querySelector('.stock-price'), base: 150.25, symbol: 'AAPL' }
    ];
    
    setInterval(() => {
        priceElements.forEach(item => {
            if (item.element) {
                const change = (Math.random() - 0.5) * 0.02; // Cambio del -1% al +1%
                item.base *= (1 + change);
                
                const isPositive = change > 0;
                item.element.textContent = item.base.toFixed(item.symbol.includes('BTC') ? 0 : 4);
                item.element.className = `font-bold ${isPositive ? 'text-green-400' : 'text-red-400'}`;
            }
        });
    }, 2000);
}

// Formulario de contacto
function setupContactForm() {
    const form = document.getElementById('contactForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Aquí integrarías con tu backend o servicio de email
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            
            // Simulación de envío exitoso
            showNotification('¡Mensaje enviado exitosamente! Te contactaré pronto.', 'success');
            form.reset();
        });
    }
}

// Sistema de notificaciones
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white font-semibold transform translate-x-full transition-transform duration-300 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        'bg-blue-500'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animar entrada
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Remover después de 5 segundos
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 5000);
}

// Calculadora de inversión
function setupInvestmentCalculator() {
    const calculator = document.getElementById('investmentCalculator');
    if (calculator) {
        const amountInput = calculator.querySelector('#investmentAmount');
        const periodSelect = calculator.querySelector('#investmentPeriod');
        const resultDiv = calculator.querySelector('#calculatorResult');
        
        function calculateReturns() {
            const amount = parseFloat(amountInput.value) || 0;
            const period = parseInt(periodSelect.value) || 12;
            
            // ROI promedio del 127% anual
            const monthlyROI = 0.127 / 12;
            const finalAmount = amount * Math.pow(1 + monthlyROI, period);
            const profit = finalAmount - amount;
            
            if (amount > 0) {
                resultDiv.innerHTML = `
                    <div class="bg-green-500/20 border border-green-500 rounded-lg p-4 mt-4">
                        <h4 class="font-bold text-green-400 mb-2">Proyección de Retornos</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <div class="text-sm text-gray-400">Inversión Inicial</div>
                                <div class="text-xl font-bold">$${amount.toLocaleString()}</div>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">Valor Final</div>
                                <div class="text-xl font-bold text-green-400">$${finalAmount.toLocaleString()}</div>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">Ganancia</div>
                                <div class="text-xl font-bold text-yellow-400">$${profit.toLocaleString()}</div>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">ROI</div>
                                <div class="text-xl font-bold text-blue-400">${((profit/amount)*100).toFixed(1)}%</div>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = '';
            }
        }
        
        amountInput.addEventListener('input', calculateReturns);
        periodSelect.addEventListener('change', calculateReturns);
    }
}

// Integración con redes sociales
function setupSocialLinks() {
    const socialLinks = {
        telegram: 'https://t.me/omarsosa_trader',
        instagram: 'https://instagram.com/omarsosa.trader',
        youtube: 'https://youtube.com/@omarsosatrader',
        twitter: 'https://twitter.com/omarsosa_trader',
        linkedin: 'https://linkedin.com/in/omarsosa-trader'
    };
    
    Object.entries(socialLinks).forEach(([platform, url]) => {
        const link = document.querySelector(`[data-social="${platform}"]`);
        if (link) {
            link.href = url;
            link.target = '_blank';
            link.rel = 'noopener noreferrer';
        }
    });
}

// Formulario de inversión
function setupInvestmentForm() {
    const form = document.getElementById('investmentForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Validaciones básicas
            if (data.amount < 1000) {
                showNotification('El monto mínimo de inversión es $1,000', 'error');
                return;
            }

            // Aquí integrarías con tu backend o CRM
            console.log('Datos de inversión:', data);

            // Simulación de envío exitoso
            showNotification('¡Solicitud enviada exitosamente! Te contactaremos pronto.', 'success');
            closeInvestmentModal();
            form.reset();

            // Redirigir a WhatsApp (opcional)
            setTimeout(() => {
                const message = `Hola Omar, quiero invertir ${data.amount} USD en el ${data.plan}. Mi nombre es ${data.fullName}.`;
                const whatsappUrl = `https://wa.me/15551234567?text=${encodeURIComponent(message)}`;
                window.open(whatsappUrl, '_blank');
            }, 2000);
        });
    }
}

// Inicialización cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar todas las funcionalidades
    animateCounters();
    createPerformanceChart();
    createPortfolioChart();
    createDetailedPerformanceChart();
    setupSmoothScrolling();
    setupScrollEffects();
    startPriceSimulator();
    setupContactForm();
    setupInvestmentForm();
    setupInvestmentCalculator();
    setupSocialLinks();

    // Mostrar mensaje de bienvenida
    setTimeout(() => {
        showNotification('¡Bienvenido al portafolio de Omar Sosa! 🚀', 'success');
    }, 2000);
});

// Funciones para la pasarela de pagos (Stripe integration)
function initializePaymentGateway() {
    // Esta función se implementaría con Stripe o la pasarela que prefieras
    console.log('Payment gateway initialized');
}

// Función para abrir modal de inversión
function openInvestmentModal() {
    const modal = document.getElementById('investmentModal');
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }
}

// Función para cerrar modal
function closeInvestmentModal() {
    const modal = document.getElementById('investmentModal');
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
}

// Event listeners para botones de inversión
document.addEventListener('click', function(e) {
    if (e.target.matches('.btn-invest, .btn-gold, .btn-primary')) {
        e.preventDefault();
        openInvestmentModal();
    }
    
    if (e.target.matches('.close-modal')) {
        closeInvestmentModal();
    }
});

// Cerrar modal al hacer click fuera
window.addEventListener('click', function(e) {
    const modal = document.getElementById('investmentModal');
    if (e.target === modal) {
        closeInvestmentModal();
    }
});
